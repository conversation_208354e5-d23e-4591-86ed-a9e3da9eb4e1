# Используем официальный образ Node.js (LTS версия, подходит для сервера с 1 ГБ RAM)
FROM node:18-slim

# Устанавливаем рабочую директорию в контейнере
WORKDIR /app

# Копируем package.json и package-lock.json для установки зависимостей
COPY package.json package-lock.json ./

# Устанавливаем зависимости
RUN npm install --production

# Копируем исходный код
COPY src ./src

# Указываем команду для запуска бота
CMD ["node", "src/index.js"]