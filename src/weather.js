const axios = require('axios');

async function getWeather(lat, lon, short = false) {
    const API_KEY = process.env.API_WEATHER_KEY;
    const url = `https://api.weather.yandex.ru/v2/informers?lat=${lat}&lon=${lon}&lang=ru_RU`;
    const headers = { 'X-Yandex-API-Key': API_KEY };

    try {
        const response = await axios.get(url, { headers });
        const data = response.data;

        // Текущая погода
        const current = data.fact;
        const temp = current.temp;
        const feelsLike = current.feels_like;
        const condition = translateCondition(current.condition);
        const windSpeed = current.wind_speed;
        const windDir = translateWindDir(current.wind_dir);

        let message = short
            ? `В Валенсии ${condition}: температура ${temp}°C, ощущается как ${feelsLike}°C, ветер ${windDir} ${windSpeed} м/с`
            : `В Волжском ${condition}: температура ${temp}°C, ощущается как ${feelsLike}°C, ветер ${windDir} ${windSpeed} м/с`;

        if (!short) {
            // Прогноз для Волжского
            const parts = data.forecast.parts;
            // Выбираем разные временные промежутки
            const part1 = parts.find(p => p.part_name === 'morning') || parts.find(p => p.part_name === 'day') || parts[0];
            const part2 = parts.find(p => p.part_name === 'evening' && p.part_name !== part1.part_name) || 
                          parts.find(p => p.part_name === 'night' && p.part_name !== part1.part_name) || 
                          parts.find(p => p.part_name !== part1.part_name) || parts[1];

            const part1Name = translatePartName(part1.part_name);
            const part2Name = translatePartName(part2.part_name);

            message += `\n${part1Name} ${translateCondition(part1.condition)}: от ${part1.temp_min}°C до ${part1.temp_max}°C, ощущается как ${part1.feels_like}°C, ветер ${translateWindDir(part1.wind_dir)} ${part1.wind_speed} м/с`;
            message += `\n${part2Name} ${translateCondition(part2.condition)}: от ${part2.temp_min}°C до ${part2.temp_max}°C, ощущается как ${part2.feels_like}°C, ветер ${translateWindDir(part2.wind_dir)} ${part2.wind_speed} м/с`;
            message += `\nВосход ${data.forecast.sunrise}, закат ${data.forecast.sunset}`;
        } else {
            // Следующий временной промежуток для Валенсии
            const parts = data.forecast.parts;
            const nextPart = parts.find(p => ['morning', 'day', 'evening', 'night'].includes(p.part_name)) || parts[0];
            const partName = translatePartName(nextPart.part_name);
            message += `\n${partName} ${translateCondition(nextPart.condition)}: от ${nextPart.temp_min}°C до ${nextPart.temp_max}°C`;
        }

        return message;
    } catch (error) {
        console.error('Ошибка получения погоды:', error.message);
        return short ? 'Не удалось получить погоду для Валенсии' : 'Не удалось получить погоду для Волжского';
    }
}

function translateCondition(condition) {
    const conditions = {
        'clear': 'ясно ☀️',
        'partly-cloudy': 'малооблачно 🌤',
        'cloudy': 'облачно с прояснениями 🌥',
        'overcast': 'пасмурно ☁️',
        'light-rain': 'небольшой дождь 🌧',
        'rain': 'дождь 💧',
        'heavy-rain': 'сильный дождь 💦',
        'showers': 'ливень ☔️',
        'wet-snow': 'дождь со снегом 💧🌨',
        'light-snow': 'небольшой снег 🌨',
        'snow': 'снег ☃️',
        'snow-showers': 'снегопад ❄️❄️',
        'hail': 'град 🧊',
        'thunderstorm': 'гроза ⚡️',
        'thunderstorm-with-rain': 'дождь с грозой ⛈',
        'thunderstorm-with-hail': 'гроза с градом 🌩🧊'
    };
    return conditions[condition] || 'неизвестно';
}

function translateWindDir(wind) {
    const directions = {
        'nw': 'северо-западный',
        'n': 'северный',
        'ne': 'северо-восточный',
        'e': 'восточный',
        'se': 'юго-восточный',
        's': 'южный',
        'sw': 'юго-западный',
        'w': 'западный',
        'c': 'отсутствует'
    };
    return directions[wind] || 'неизвестно';
}

function translatePartName(part) {
    const parts = {
        'night': '🌚 Ночью',
        'morning': '🌅 Утром',
        'day': '🌞 Днём',
        'evening': '🌗 Вечером'
    };
    return parts[part] || 'неизвестно';
}

module.exports = { getWeather };