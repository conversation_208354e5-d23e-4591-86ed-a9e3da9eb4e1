const axios = require('axios');
const cheerio = require('cheerio');
const { getWeather } = require('./weather');
const { config } = require('./config');
const iconv = require('iconv-lite');

async function getCurrencyRates() {
    const url = 'https://www.cbr-xml-daily.ru/daily_json.js';
    try {
        const response = await axios.get(url, { timeout: config.TIMEOUTS.HTTP_REQUEST });
        const data = response.data;
        const usdRate = data.Valute.USD.Value;
        const eurRate = data.Valute.EUR.Value;
        return { usd: usdRate, eur: eurRate };
    } catch (error) {
        console.error('Ошибка получения курсов валют:', error.message);
        return { usd: null, eur: null };
    }
}

function getDate() {
    const dayList = [
        'первое', 'второе', 'третье', 'четвёртое', 'пятое', 'шестое', 'седьмое', 'восьмое',
        'девятое', 'десятое', 'одиннадцатое', 'двенадцатое', 'тринадцатое', 'четырнадцатое',
        'пятнадцатое', 'шестнадцатое', 'семнадцатое', 'восемнадцатое', 'девятнадцатое', 'двадцатое',
        'двадцать первое', 'двадцать второе', 'двадцать третье', 'двадцать четвёртое', 'двадцать пятое',
        'двадцать шестое', 'двадцать седьмое', 'двадцать восьмое', 'двадцать девятое', 'тридцатое', 'тридцать первое'
    ];
    const monthList = [
        'января', 'февраля', 'марта', 'апреля', 'мая', 'июня',
        'июля', 'августа', 'сентября', 'октября', 'ноября', 'декабря'
    ];

    const now = new Date().toLocaleString('en-US', { timeZone: 'Europe/Moscow' });
    const [month, day, year] = now.split(',')[0].split('/');
    return `${dayList[parseInt(day) - 1]} ${monthList[parseInt(month) - 1]} ${year} года`;
}

async function getNews() {
    try {
        const response = await axios.get('https://www.volzsky.ru/', {
            responseType: 'arraybuffer',
            timeout: config.TIMEOUTS.HTTP_REQUEST
        });
        const html = iconv.decode(Buffer.from(response.data), 'windows-1251');
        const $ = cheerio.load(html);
        const newsItems = $('.btc_h a').slice(0, 5);
        let newsStr = 'В сегодняшнем выпуске ежедневного пророка:\n';
        newsItems.each((index, el) => {
            const title = $(el).text().trim();
            const href = $(el).attr('href');
            newsStr += `>><a href="https://www.volzsky.ru/${href}">${title}</a>\n`;
        });
        return newsStr;
    } catch (error) {
        console.error('Ошибка получения новостей:', error.message);
        return 'Не удалось загрузить новости.\n';
    }
}

async function getFactOfTheDay() {
    try {
        const now = new Date().toLocaleString('en-US', { timeZone: 'Europe/Moscow' });
        const [month, day] = now.split(',')[0].split('/').map(Number);
        const url = `https://ru.wikipedia.org/api/rest_v1/feed/onthisday/events/${month}/${day}`;
        const response = await axios.get(url, { timeout: config.TIMEOUTS.HTTP_REQUEST });
        const events = response.data.events;

        if (events && events.length > 0) {
            // Фильтруем события: с текстом, годом и до 2000 года
            const validEvents = events.filter(e => e.text && e.year && e.year < 2000);
            if (validEvents.length === 0) {
                return 'Факт дня: Сегодня ничего интересного не произошло до 2000 года.';
            }

            // Выбираем случайное событие
            const randomIndex = Math.floor(Math.random() * validEvents.length);
            const event = validEvents[randomIndex];

            return `Факт дня: В ${event.year} году ${event.text}`;
        }

        return 'Факт дня: Сегодня ничего интересного не произошло до 2000 года.';
    } catch (error) {
        console.error('Ошибка получения факта дня:', error.message);
        return 'Факт дня: Не удалось загрузить факт.';
    }
}

async function sendDailyMessage(isMorning = true) {
    const mscTimezone = 'Europe/Moscow';
    const now = new Date().toLocaleString('en-US', { timeZone: mscTimezone });
    const currentDate = getDate();
    const greeting = isMorning ? 'Доброе утро отряд!' : 'Добрый вечер отряд!';
    let message = `${greeting} Сегодня ${currentDate}\n`;

    // Курсы валют
    const { usd, eur } = await getCurrencyRates();
    message += `USD: ${usd ? usd.toFixed(2) : 'н/д'} руб. EUR: ${eur ? eur.toFixed(2) : 'н/д'} руб.\n🇷🇺\n`;

    // Погода для Волжского
    const { lat: volzhskyLat, lon: volzhskyLon } = config.CITIES.VOLZHSKY;
    const volzhskyWeather = await getWeather(volzhskyLat, volzhskyLon, false);
    message += `${volzhskyWeather}\n🇪🇸\n`;

    // Погода для Валенсии (Испания)
    const { lat: valenciaLat, lon: valenciaLon } = config.CITIES.VALENCIA;
    const valenciaWeather = await getWeather(valenciaLat, valenciaLon, true);
    message += `${valenciaWeather}\n`;

    // Новости
    const newsMessage = await getNews();
    message += newsMessage;

    // Факт дня
    const factMessage = await getFactOfTheDay();
    message += `📜 ${factMessage}\n`;

    // Напоминание о счётчиках
    const currentMonth = parseInt(now.split(',')[0].split('/')[0]);
    const currentDay = parseInt(now.split(',')[0].split('/')[1]);
    if (currentMonth !== 12) {
        if (currentDay >= 23 && currentDay <= 25) {
            message += '<a href="https://lk.kvitel.ru/">🚰 НЕ ЗАБЫВАЕМ ОТПРАВИТЬ ИНФОРМАЦИЮ О СЧЁТЧИКАХ</a>\n';
        }
    } else {
        if (currentDay >= 20 && currentDay <= 24) {
            message += '<a href="https://lk.kvitel.ru/">🚰 НЕ ЗАБЫВАЕМ ОТПРАВИТЬ ИНФОРМАЦИЮ О СЧЁТЧИКАХ</a>\n';
        }
    }

    message += '#Hashsquadtag';

    return { message, currentDate };
}

async function getWeirdNews() {
    try {
        const url = 'https://disgustingmen.com/tag/dich/';
        const response = await axios.get(url, {
            responseType: 'arraybuffer',
            headers: { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' },
            timeout: config.TIMEOUTS.HTTP_REQUEST
        });
        // Декодируем ответ (сайт использует UTF-8)
        const html = iconv.decode(Buffer.from(response.data), 'utf-8');
        const $ = cheerio.load(html);

        // Находим первый <article> с классом category-blog
        const article = $('article.category-blog').first();
        if (!article.length) {
            return { message: 'Не удалось найти новости на сайте.' };
        }

        // Извлекаем содержимое <div class="entry-content">
        const content = article.find('div.entry-content').html();
        if (!content) {
            return { message: 'Не удалось извлечь текст новости.' };
        }

        // Парсим содержимое для получения текста и пунктов
        const $content = cheerio.load(content);
        let text = $content('p').first().text().trim(); // Первый параграф

        // Извлекаем второй <p> и обрабатываем его содержимое
        const secondParagraph = $content('p').eq(1);
        const listItems = [];
        let currentText = '';

        secondParagraph.contents().each((index, node) => {
            if (node.type === 'text') {
                // Добавляем текстовый узел
                currentText += node.data.trim();
            } else if (node.name === 'br') {
                // При встрече <br> сохраняем накопленный текст как пункт
                if (currentText) {
                    listItems.push(`— ${currentText.replace(/;$/, '')}`);
                    currentText = '';
                }
            }
        });

        // Добавляем последний пункт, если текст остался
        if (currentText) {
            listItems.push(`— ${currentText.replace(/;$/, '')}`);
        }

        // Извлекаем ссылку "Читать далее"
        const link = article.find('a.more-link').attr('href') || '#';

        // Формируем сообщение
        const message = `
${text}

${listItems.join('\n')}

<i><a href="${link}">Читать далее →</a></i>
        `.trim();

        return { message };
    } catch (error) {
        console.error('Ошибка получения новости "Дичь":', error.message);
        return { message: 'Не удалось загрузить новость "Дичь".' };
    }
}

async function getMuseNews() {
    try {
        const url = 'https://disgustingmen.com/girls/';
        const response = await axios.get(url, {
            responseType: 'arraybuffer',
            headers: { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' },
            timeout: config.TIMEOUTS.HTTP_REQUEST
        });
        // Декодируем ответ (сайт использует UTF-8)
        const html = iconv.decode(Buffer.from(response.data), 'utf-8');
        const $ = cheerio.load(html);

        // Находим первый <article> с классом category-girls
        const article = $('article.category-girls').first();
        if (!article.length) {
            return { message: 'Не удалось найти новости на сайте.' };
        }

        // Извлекаем заголовок из <h2 class="entry-title">
        const title = article.find('h2.entry-title a').text().trim();
        if (!title) {
            return { message: 'Не удалось извлечь заголовок новости.' };
        }

        // Извлекаем содержимое <div class="entry-content">
        const content = article.find('div.entry-content').html();
        if (!content) {
            return { message: 'Не удалось извлечь текст новости.' };
        }

        // Парсим содержимое для получения текста параграфов
        const $content = cheerio.load(content);
        const paragraphs = $content('p')
            .map((index, el) => $(el).text().trim())
            .get()
            .filter(text => text); // Удаляем пустые параграфы

        // Извлекаем ссылку "Читать далее"
        const link = article.find('a.more-link').attr('href') || '#';

        // Формируем сообщение
        const message = `
${title}

${paragraphs.join('\n\n')}

<i><a href="${link}">Читать далее →</a></i>
        `.trim();

        return { message };
    } catch (error) {
        console.error('Ошибка получения новости "Муза":', error.message);
        return { message: 'Не удалось загрузить новость "Муза".' };
    }
}

async function getRandomBashQuote() {
    try {
        // Вспомогательная функция для генерации случайного периода
        const generateRandomPeriod = () => {
            const startYear = 2004;
            const endYear = 2020;
            const year = Math.floor(Math.random() * (endYear - startYear + 1)) + startYear;
            let month;
            if (year === 2004) {
                month = Math.floor(Math.random() * 5) + 8; // 8–12
            } else {
                month = Math.floor(Math.random() * 12) + 1; // 1–12
            }
            return { year, month };
        };

        let quotes;
        let attempts = 0;
        const maxAttempts = 5;

        // Пытаемся загрузить цитаты до maxAttempts раз
        while (attempts < maxAttempts) {
            const { year, month } = generateRandomPeriod();
            const url = `https://xn--80abh7bk0c.xn--p1ai/best/${year}/${month}`;
            console.log(`Attempt ${attempts + 1}: Loading quotes from ${url}`); // Для отладки

            const response = await axios.get(url, {
                responseType: 'arraybuffer',
                headers: { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' },
                timeout: config.TIMEOUTS.HTTP_REQUEST
            });

            // Декодируем ответ (сайт использует UTF-8)
            const html = iconv.decode(Buffer.from(response.data), 'utf-8');
            const $ = cheerio.load(html);

            // Находим все цитаты
            quotes = $('article.quote');
            if (quotes.length > 0) {
                break; // Цитаты найдены, выходим из цикла
            }

            attempts++;
            console.log(`No quotes found for ${year}/${month}, retrying...`); // Для отладки
        }

        // Если цитаты не найдены после всех попыток
        if (!quotes || quotes.length === 0) {
            return { message: 'Не удалось найти цитаты после нескольких попыток.' };
        }

        // Выбираем случайную цитату
        const randomIndex = Math.floor(Math.random() * quotes.length);
        const quote = quotes.eq(randomIndex);

        // Извлекаем номер цитаты
        const quoteId = quote.find('a.quote__header_permalink').text().trim() || '???';

        // Извлекаем текст цитаты
        const quoteBody = quote.find('div.quote__body').html();
        if (!quoteBody) {
            return { message: 'Не удалось извлечь текст цитаты.' };
        }

        // Обрабатываем HTML: преобразуем теги и форматируем
        let formattedQuote = quoteBody
            .replace(/<br\s*\/?>/gi, '\n') // <br> → новая строка
            .replace(/<\/?[^>]+>/gi, '') // Удаляем все HTML-теги
            .replace(/</g, '<') // Экранируем <
            .replace(/>/g, '>') // Экранируем >
            .trim();

        // Формируем сообщение
        const message = `Цитата ${quoteId}:\n${formattedQuote}`;

        // Проверяем наличие комикса
        const comic = quote.find('div.quote__strips img.quote__strips_img');
        const comicUrl = comic.length ? `https://xn--80abh7bk0c.xn--p1ai${comic.attr('src').replace('/ts/', '/')}` : null;

        return { message, comicUrl };
    } catch (error) {
        console.error('Ошибка получения цитаты с Башорга:', error.message);
        return { message: 'Не удалось загрузить цитату с Башорга.' };
    }
}

module.exports = { getWeather, getCurrencyRates, getNews, getFactOfTheDay, sendDailyMessage, getWeirdNews, getMuseNews, getRandomBashQuote };