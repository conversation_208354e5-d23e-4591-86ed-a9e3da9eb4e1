const fs = require('fs').promises;
const path = require('path');
const { config } = require('./config');

class DuelManager {
    constructor() {
        this.duelActive = false;
        this.results = new Map();
        this.dataDir = config.DATA_PATHS.DIR;
        this.duelResultsFile = config.DATA_PATHS.DUEL_RESULTS;
        this.hallOfFameFile = config.DATA_PATHS.HALL_OF_FAME;
    }

    // Начать новую дуэль
    async startDuel() {
        this.duelActive = true;
        this.results.clear();
        await this.saveResults();
    }

    // Добавить результат броска
    async addResult(userId, username, diceValue) {
        if (!this.duelActive) return false;
        if (this.results.has(userId)) return false; // Только один бросок на пользователя
        this.results.set(userId, { username, diceValue });
        await this.saveResults();
        return true;
    }

    // Получить результаты и определить победителей
    async getResults() {
        const results = Array.from(this.results.entries()).map(([userId, data]) => ({
            userId,
            username: data.username,
            diceValue: data.diceValue
        }));

        if (results.length === 0) {
            return { results: [], winners: [] };
        }

        const maxDice = Math.max(...results.map(r => r.diceValue));
        const winners = results.filter(r => r.diceValue === maxDice);

        return { results, winners };
    }

    // Завершить дуэль
    async endDuel() {
        this.duelActive = false;
        const { results, winners } = await this.getResults();
        for (const winner of winners) {
            await this.addToHallOfFame(winner.userId, winner.username);
        }
        await this.saveResults();
        return { results, winners };
    }

    // Сохранить результаты в файл
    async saveResults() {
        const data = {
            duelActive: this.duelActive,
            results: Array.from(this.results.entries()).map(([userId, data]) => ({
                userId,
                username: data.username,
                diceValue: data.diceValue
            }))
        };
        try {
            await fs.mkdir(this.dataDir, { recursive: true }); // Создаём папку, если не существует
            await fs.writeFile(this.duelResultsFile, JSON.stringify(data, null, 2));
            console.log(`Saved duel_results.json:`, data);
        } catch (error) {
            console.error('Ошибка сохранения результатов дуэли:', error.message);
        }
    }

    // Загрузить результаты из файла
    async loadResults() {
        try {
            const data = await fs.readFile(this.duelResultsFile, 'utf8');
            const parsed = JSON.parse(data);
            this.duelActive = parsed.duelActive || false;
            this.results = new Map(parsed.results.map(r => [r.userId, { username: r.username, diceValue: r.diceValue }]));
            console.log(`Loaded duel_results.json:`, parsed);
        } catch (error) {
            console.warn('duel_results.json не найден или повреждён, инициализация пустых результатов:', error.message);
            this.results = new Map();
            this.duelActive = false;
        }
    }

    // Добавить победителя в зал славы
    async addToHallOfFame(userId, username) {
        let hallOfFame = await this.loadHallOfFame();
        if (!hallOfFame[userId]) {
            hallOfFame[userId] = { username, wins: 0 };
        }
        hallOfFame[userId].wins += 1;
        try {
            await fs.mkdir(this.dataDir, { recursive: true }); // Создаём папку, если не существует
            await fs.writeFile(this.hallOfFameFile, JSON.stringify(hallOfFame, null, 2));
            console.log(`Saved hall_of_fame.json:`, hallOfFame);
        } catch (error) {
            console.error('Ошибка сохранения зала славы:', error.message);
        }
    }

    // Загрузить зал славы
    async loadHallOfFame() {
        try {
            const data = await fs.readFile(this.hallOfFameFile, 'utf8');
            const parsed = JSON.parse(data);
            console.log(`Loaded hall_of_fame.json:`, parsed);
            return parsed;
        } catch (error) {
            console.warn('hall_of_fame.json не найден или повреждён, инициализация пустого зала славы:', error.message);
            return {};
        }
    }

    // Получить зал славы
    async getHallOfFame() {
        const hallOfFame = await this.loadHallOfFame();
        return Object.entries(hallOfFame).map(([userId, data]) => ({
            userId,
            username: data.username,
            wins: data.wins
        }));
    }
}

module.exports = new DuelManager();