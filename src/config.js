// Конфигурация и константы для бота
const config = {
    // Координаты городов
    CITIES: {
        VOLZHSKY: { lat: 48.81765, lon: 44.77073, name: '<PERSON><PERSON><PERSON><PERSON><PERSON>к<PERSON>' },
        VALENCIA: { lat: 39.4699, lon: -0.3763, name: 'Валенсия' }
    },

    // Расписание (в UTC)
    SCHEDULES: {
        MORNING_MESSAGE: '0 0 5 * * *',    // 08:00 MSK
        EVENING_MESSAGE: '0 0 17 * * *',   // 20:00 MSK
        DUEL_START: '0 0 9 * * *',         // 12:00 MSK
        DUEL_END: '0 0 12 * * *',          // 15:00 MSK
        BASH_QUOTES_RESET: '0 0 0 * * *',   // 00:00 UTC
        // Проверки новостей (MSK → UTC: -3 часа)
        WEIRD_NEWS_CHECK: [
            '0 0 8 * * 1',  // 11:00 MSK понедельник
            '0 0 11 * * 1', // 14:00 MSK понедельник
            '0 0 14 * * 1', // 17:00 MSK понедельник
            '0 0 18 * * 1'  // 21:00 MSK понедельник
        ],
        MUSE_NEWS_CHECK: [
            '0 0 8 * * 5',  // 11:00 MSK пятница
            '0 0 11 * * 5', // 14:00 MSK пятница
            '0 0 14 * * 5', // 17:00 MSK пятница
            '0 0 18 * * 5'  // 21:00 MSK пятница
        ]
    },

    // Настройки дуэли
    DUEL: {
        MIN_INTERVAL_MINUTES: 30,
        QUOTES_PER_DAY: 3,
        TIMEZONE: 'Europe/Paris'
    },

    // Таймауты и лимиты
    TIMEOUTS: {
        HTTP_REQUEST: 10000,
        DICE_RESPONSE: 3000
    },

    // Пути к файлам данных
    DATA_PATHS: {
        DIR: '/app/data',
        DUEL_RESULTS: '/app/data/duel_results.json',
        HALL_OF_FAME: '/app/data/hall_of_fame.json',
        LAST_WEIRD_NEWS: '/app/data/last_weird_news.json',
        LAST_MUSE_NEWS: '/app/data/last_muse_news.json'
    }
};

// Валидация переменных окружения
const validateEnv = () => {
    const requiredVars = ['TELEGRAM_BOT_TOKEN', 'GROUP_ID', 'ADMIN_ID', 'API_WEATHER_KEY'];
    const missing = requiredVars.filter(varName => !process.env[varName]);

    if (missing.length > 0) {
        console.error(`❌ Отсутствуют обязательные переменные окружения: ${missing.join(', ')}`);
        process.exit(1);
    }

    console.log('✅ Все переменные окружения настроены правильно');
};

module.exports = { config, validateEnv }; 