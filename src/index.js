require('dotenv').config();
const Bot = require('./bot');
const { validateEnv } = require('./config');

// Валидация переменных окружения
validateEnv();

const token = process.env.TELEGRAM_BOT_TOKEN;

try {
    const bot = new Bot(token);
    console.log('🤖 Бот успешно запущен и готов к работе');

    // Graceful shutdown
    process.on('SIGINT', () => {
        console.log('🛑 Получен сигнал SIGINT. Завершение работы...');
        process.exit(0);
    });

    process.on('SIGTERM', () => {
        console.log('🛑 Получен сигнал SIGTERM. Завершение работы...');
        process.exit(0);
    });

} catch (error) {
    console.error('❌ Ошибка запуска бота:', error.message);
    process.exit(1);
}