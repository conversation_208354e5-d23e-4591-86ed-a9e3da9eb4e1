const axios = require('axios');
const cheerio = require('cheerio');
const iconv = require('iconv-lite');
const fs = require('fs').promises;
const { config } = require('./config');

class NewsChecker {
    constructor() {
        this.lastWeirdNewsFile = config.DATA_PATHS.LAST_WEIRD_NEWS;
        this.lastMuseNewsFile = config.DATA_PATHS.LAST_MUSE_NEWS;
    }

    // Получить текущую дату в формате dd.mm.yyyy
    getCurrentDate() {
        const now = new Date();
        const day = String(now.getDate()).padStart(2, '0');
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const year = now.getFullYear();
        return `${day}.${month}.${year}`;
    }

    // Получить день недели (1 = понедельник, 5 = пятница)
    getCurrentDayOfWeek() {
        const now = new Date();
        return now.getDay() === 0 ? 7 : now.getDay(); // Воскресенье = 7
    }

    // Сохранить информацию о последней отправленной новости
    async saveLastNews(type, date, postId) {
        const filePath = type === 'weird' ? this.lastWeirdNewsFile : this.lastMuseNewsFile;
        const data = { date, postId, timestamp: Date.now() };

        try {
            await fs.mkdir(config.DATA_PATHS.DIR, { recursive: true });
            await fs.writeFile(filePath, JSON.stringify(data, null, 2));
            console.log(`💾 Saved last ${type} news: ${date}, postId: ${postId}`);
        } catch (error) {
            console.error(`❌ Error saving last ${type} news:`, error.message);
        }
    }

    // Загрузить информацию о последней отправленной новости
    async loadLastNews(type) {
        const filePath = type === 'weird' ? this.lastWeirdNewsFile : this.lastMuseNewsFile;

        try {
            const data = await fs.readFile(filePath, 'utf8');
            const parsed = JSON.parse(data);
            console.log(`📁 Loaded last ${type} news:`, parsed);
            return parsed;
        } catch (error) {
            console.log(`📁 No previous ${type} news data found, starting fresh`);
            return null;
        }
    }

    // Проверить новости "Дичь"
    async checkWeirdNews() {
        const currentDate = this.getCurrentDate();
        const dayOfWeek = this.getCurrentDayOfWeek();

        console.log(`🔍 Checking weird news for ${currentDate} (day ${dayOfWeek})`);

        // Проверяем только в понедельник
        if (dayOfWeek !== 1) {
            console.log('📅 Not Monday, skipping weird news check');
            return null;
        }

        try {
            const url = 'https://disgustingmen.com/tag/dich/';
            const response = await axios.get(url, {
                responseType: 'arraybuffer',
                headers: { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' },
                timeout: config.TIMEOUTS.HTTP_REQUEST
            });

            const html = iconv.decode(Buffer.from(response.data), 'utf-8');
            const $ = cheerio.load(html);

            // Находим первую статью
            const article = $('article.category-blog').first();
            if (!article.length) {
                console.log('❌ No weird news articles found');
                return null;
            }

            // Извлекаем дату
            const dateElement = article.find('time.entry-date');
            const articleDate = dateElement.text().trim();

            if (articleDate !== currentDate) {
                console.log(`📅 Article date (${articleDate}) != current date (${currentDate})`);
                return null;
            }

            // Извлекаем ID поста
            const postId = article.attr('id')?.replace('post-', '') ||
                article.find('a.more-link').attr('href')?.match(/#more-(\d+)/)?.[1];

            if (!postId) {
                console.log('❌ Could not extract post ID');
                return null;
            }

            // Проверяем, не отправляли ли уже эту новость
            const lastNews = await this.loadLastNews('weird');
            if (lastNews && lastNews.postId === postId) {
                console.log(`✅ Weird news already sent today (postId: ${postId})`);
                return null;
            }

            // Извлекаем заголовок
            const title = article.find('h2.entry-title a').text().trim();

            // Извлекаем содержимое
            const content = article.find('div.entry-content').html();
            if (!content) {
                console.log('❌ Could not extract content');
                return null;
            }

            // Парсим содержимое
            const $content = cheerio.load(content);
            let text = $content('p').first().text().trim();

            // Извлекаем список пунктов из второго параграфа
            const secondParagraph = $content('p').eq(1);
            const listItems = [];
            let currentText = '';

            secondParagraph.contents().each((index, node) => {
                if (node.type === 'text') {
                    currentText += node.data.trim();
                } else if (node.name === 'br') {
                    if (currentText) {
                        listItems.push(`— ${currentText.replace(/;$/, '')}`);
                        currentText = '';
                    }
                }
            });

            if (currentText) {
                listItems.push(`— ${currentText.replace(/;$/, '')}`);
            }

            // Извлекаем ссылку
            const link = article.find('a.more-link').attr('href') || '#';

            // Формируем сообщение
            const message = `
${title}

${text}

${listItems.join('\n')}

<i><a href="${link}">Читать далее →</a></i>
            `.trim();

            // Сохраняем информацию о новости
            await this.saveLastNews('weird', currentDate, postId);

            return { message, type: 'weird' };

        } catch (error) {
            console.error('❌ Error checking weird news:', error.message);
            return null;
        }
    }

    // Проверить новости "Муза"
    async checkMuseNews() {
        const currentDate = this.getCurrentDate();
        const dayOfWeek = this.getCurrentDayOfWeek();

        console.log(`🔍 Checking muse news for ${currentDate} (day ${dayOfWeek})`);

        // Проверяем только в пятницу
        if (dayOfWeek !== 5) {
            console.log('📅 Not Friday, skipping muse news check');
            return null;
        }

        try {
            const url = 'https://disgustingmen.com/girls/';
            const response = await axios.get(url, {
                responseType: 'arraybuffer',
                headers: { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' },
                timeout: config.TIMEOUTS.HTTP_REQUEST
            });

            const html = iconv.decode(Buffer.from(response.data), 'utf-8');
            const $ = cheerio.load(html);

            // Находим первую статью
            const article = $('article.category-girls').first();
            if (!article.length) {
                console.log('❌ No muse news articles found');
                return null;
            }

            // Извлекаем дату
            const dateElement = article.find('time.entry-date');
            const articleDate = dateElement.text().trim();

            if (articleDate !== currentDate) {
                console.log(`📅 Article date (${articleDate}) != current date (${currentDate})`);
                return null;
            }

            // Извлекаем ID поста
            const postId = article.attr('id')?.replace('post-', '') ||
                article.find('a.more-link').attr('href')?.match(/#more-(\d+)/)?.[1];

            if (!postId) {
                console.log('❌ Could not extract post ID');
                return null;
            }

            // Проверяем, не отправляли ли уже эту новость
            const lastNews = await this.loadLastNews('muse');
            if (lastNews && lastNews.postId === postId) {
                console.log(`✅ Muse news already sent today (postId: ${postId})`);
                return null;
            }

            // Извлекаем заголовок
            const title = article.find('h2.entry-title a').text().trim();
            if (!title) {
                console.log('❌ Could not extract title');
                return null;
            }

            // Извлекаем содержимое
            const content = article.find('div.entry-content').html();
            if (!content) {
                console.log('❌ Could not extract content');
                return null;
            }

            // Парсим содержимое для получения текста параграфов
            const $content = cheerio.load(content);
            const paragraphs = $content('p')
                .map((index, el) => $(el).text().trim())
                .get()
                .filter(text => text); // Удаляем пустые параграфы

            // Извлекаем ссылку
            const link = article.find('a.more-link').attr('href') || '#';

            // Формируем сообщение
            const message = `
${title}

${paragraphs.join('\n\n')}

<i><a href="${link}">Читать далее →</a></i>
            `.trim();

            // Сохраняем информацию о новости
            await this.saveLastNews('muse', currentDate, postId);

            return { message, type: 'muse' };

        } catch (error) {
            console.error('❌ Error checking muse news:', error.message);
            return null;
        }
    }
}

module.exports = new NewsChecker(); 