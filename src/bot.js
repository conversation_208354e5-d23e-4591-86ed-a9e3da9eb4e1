const TelegramBot = require('node-telegram-bot-api');
const { getWeather } = require('./weather');
const { sendDailyMessage, getCurrencyRates, getWeirdNews, getMuseNews, getRandomBashQuote } = require('./dailyMessage');
const schedule = require('node-schedule');
const duelManager = require('./duel');
const newsChecker = require('./newsChecker');
const { config } = require('./config');

class Bot {
    constructor(token) {
        this.bot = new TelegramBot(token, { polling: true });
        this.groupChatId = process.env.GROUP_ID;
        this.adminId = process.env.ADMIN_ID;
        this.setupHandlers();
        this.setupSchedules();
    }

    setupHandlers() {
        // Обработка команды /start
        this.bot.onText(/\/start/, (msg) => {
            const chatId = msg.chat.id;
            this.bot.sendMessage(chatId, 'Привет! Я твой новый бот. Напиши /help, чтобы узнать, что я умею.');
        });

        // Обработка команды /help
        this.bot.onText(/\/help/, (msg) => {
            const chatId = msg.chat.id;
            this.bot.sendMessage(chatId, 'Список команд:\n/start - Начать работу\n/help - Показать помощь\n/weather - Показать погоду\n/admin - Меню администратора (только для админа)\n!hof - Зал славы дуэлей');
        });

        // Обработка команды /weather
        this.bot.onText(/\/weather/, async (msg) => {
            const chatId = msg.chat.id;
            const { lat, lon } = config.CITIES.VOLZHSKY;
            const weatherMessage = await getWeather(lat, lon, false);
            this.bot.sendMessage(chatId, weatherMessage);
        });

        // Обработка команды /admin (только в личных сообщениях)
        this.bot.onText(/\/admin/, (msg) => {
            const chatId = msg.chat.id;
            const userId = msg.from.id.toString();

            // Проверка, что это личный чат и пользователь — админ
            if (msg.chat.type !== 'private' || userId !== this.adminId) {
                this.bot.sendMessage(chatId, 'Эта команда доступна только администратору в личных сообщениях.');
                return;
            }

            // Создание интерактивного меню
            const keyboard = {
                inline_keyboard: [
                    [
                        { text: 'Отправить погоду', callback_data: 'send_weather' },
                        { text: 'Отправить валюты', callback_data: 'send_currencies' }
                    ],
                    [
                        { text: 'Отправить полное сообщение', callback_data: 'send_daily' },
                        { text: 'Дичь', callback_data: 'send_weird_news' }
                    ],
                    [
                        { text: 'Муза', callback_data: 'send_muse_news' },
                        { text: 'Башорг', callback_data: 'send_bash_quote' }
                    ]
                ]
            };

            this.bot.sendMessage(chatId, 'Меню администратора:', { reply_markup: keyboard });
        });

        // Обработка команды !баш
        this.bot.onText(/!баш/, async (msg) => {
            const { message, comicUrl } = await getRandomBashQuote();
            await this.bot.sendMessage(this.groupChatId, message, {
                parse_mode: 'HTML',
                disable_web_page_preview: true
            });

            if (comicUrl) {
                await this.bot.sendPhoto(this.groupChatId, comicUrl);
            }
        });

        // Обработка нажатий на кнопки меню
        this.bot.on('callback_query', async (query) => {
            const chatId = query.message.chat.id;
            const userId = query.from.id.toString();
            const data = query.data;

            // Проверка, что это админ
            if (userId !== this.adminId) {
                this.bot.answerCallbackQuery(query.id, { text: 'Доступ запрещён!' });
                return;
            }

            // Обработка действий
            if (data === 'send_weather') {
                const { lat, lon } = config.CITIES.VOLZHSKY;
                const weatherMessage = await getWeather(lat, lon, false);
                await this.bot.sendMessage(this.groupChatId, weatherMessage);
                this.bot.answerCallbackQuery(query.id, { text: 'Погода отправлена в группу!' });
            } else if (data === 'send_currencies') {
                const { usd, eur } = await getCurrencyRates();
                const message = `USD: ${usd ? usd.toFixed(2) : 'н/д'} руб. EUR: ${eur ? eur.toFixed(2) : 'н/д'} руб.\n🇷🇺`;
                await this.bot.sendMessage(this.groupChatId, message);
                this.bot.answerCallbackQuery(query.id, { text: 'Курсы валют отправлены в группу!' });
            } else if (data === 'send_daily') {
                const now = new Date();
                // Устанавливаем MSK (UTC+3) вручную, если toLocaleString ненадёжен
                const mskOffset = 3 * 60 * 60 * 1000; // 3 часа в миллисекундах
                const mskTime = new Date(now.getTime() + mskOffset);
                const currentHour = mskTime.getHours();
                console.log('MSK time:', mskTime.toISOString(), 'currentHour:', currentHour); // Отладка
                const isMorning = currentHour < 20; // До 20:00 — утро, после — вечер
                const { message } = await sendDailyMessage(isMorning);
                await this.bot.sendMessage(this.groupChatId, message, {
                    parse_mode: 'HTML',
                    disable_web_page_preview: true
                });
                this.bot.answerCallbackQuery(query.id, { text: `Полное ${isMorning ? 'утреннее' : 'вечернее'} сообщение отправлено в группу!` });
            } else if (data === 'send_weird_news') {
                const { message } = await getWeirdNews();
                await this.bot.sendMessage(this.groupChatId, message, {
                    parse_mode: 'HTML',
                    disable_web_page_preview: true
                });
                this.bot.answerCallbackQuery(query.id, { text: 'Новость "Дичь" отправлена в группу!' });
            } else if (data === 'send_muse_news') {
                const { message } = await getMuseNews();
                await this.bot.sendMessage(this.groupChatId, message, {
                    parse_mode: 'HTML',
                    disable_web_page_preview: true
                });
                this.bot.answerCallbackQuery(query.id, { text: 'Новость "Муза" отправлена в группу!' });
            } else if (data === 'send_bash_quote') {
                const { message, comicUrl } = await getRandomBashQuote();
                await this.bot.sendMessage(this.groupChatId, message, {
                    parse_mode: 'HTML',
                    disable_web_page_preview: true
                });
                if (comicUrl) {
                    await this.bot.sendPhoto(this.groupChatId, comicUrl);
                }
                this.bot.answerCallbackQuery(query.id, { text: 'Цитата с Башорга отправлена в группу!' });
            }

            // Удаление сообщения с меню после действия
            this.bot.deleteMessage(chatId, query.message.message_id);
        });
    }

    setupSchedules() {
        // Загрузка результатов дуэли при старте
        duelManager.loadResults().then(() => console.log('Duel results loaded'));

        // Утреннее сообщение в 08:00 MSK (05:00 UTC)
        schedule.scheduleJob(config.SCHEDULES.MORNING_MESSAGE, async () => {
            try {
                const { message } = await sendDailyMessage(true);
                this.bot.sendMessage(this.groupChatId, message, {
                    parse_mode: 'HTML',
                    disable_web_page_preview: true
                });
            } catch (error) {
                console.error('Ошибка отправки утреннего сообщения:', error.message);
            }
        });

        // Вечернее сообщение в 20:00 MSK (17:00 UTC)
        schedule.scheduleJob(config.SCHEDULES.EVENING_MESSAGE, async () => {
            try {
                const { message } = await sendDailyMessage(false);
                this.bot.sendMessage(this.groupChatId, message, {
                    parse_mode: 'HTML',
                    disable_web_page_preview: true
                });
            } catch (error) {
                console.error('Ошибка отправки вечернего сообщения:', error.message);
            }
        });

        // Расписание для дуэли: начало в 12:00 MSK (09:00 UTC)
        schedule.scheduleJob(config.SCHEDULES.DUEL_START, async () => {
            await duelManager.startDuel();
            const message = 'Внимание, полдень! Время для дуэли, нажмите на кубик для броска. Победителю вечная слава, проигравшим забвение. Время дуэли до 15:00.';
            await this.bot.sendMessage(this.groupChatId, message, { parse_mode: 'HTML' });
            await this.bot.sendDice(this.groupChatId, { emoji: '🎲' }); // Отправка демонстрационного кубика
        });

        // Расписание для дуэли: итоги в 15:00 MSK (12:00 UTC)
        schedule.scheduleJob(config.SCHEDULES.DUEL_END, async () => {
            const { results, winners } = await duelManager.endDuel();
            let message = 'Дуэль завершена! Результаты:\n';
            if (results.length === 0) {
                message += 'Никто не участвовал в дуэли. 😔';
            } else {
                results.forEach(r => {
                    message += `${r.username}: ${r.diceValue}\n`;
                });
                if (winners.length === 1) {
                    message += `\n🏆 Победитель: ${winners[0].username} с результатом ${winners[0].diceValue}!`;
                } else if (winners.length > 1) {
                    const winnerNames = winners.map(w => w.username).join(', ');
                    message += `\n🏆 Победители: ${winnerNames} с результатом ${winners[0].diceValue}!`;
                } else {
                    message += '\n🏆 Победителей нет.';
                }
            }
            this.bot.sendMessage(this.groupChatId, message);
        });

        // Ежедневное создание трёх шедулеров для случайных цитат
        schedule.scheduleJob(config.SCHEDULES.BASH_QUOTES_RESET, () => {
            console.log('🔄 Starting daily bash quotes scheduler reset...');

            // Очищаем ВСЕ существующие задания с цитатами
            const existingJobs = Object.keys(schedule.scheduledJobs);
            const bashJobs = existingJobs.filter(jobName => jobName.startsWith('bash_quote_'));

            console.log(`Found ${bashJobs.length} existing bash quote jobs:`, bashJobs);

            bashJobs.forEach(jobName => {
                schedule.scheduledJobs[jobName].cancel();
                delete schedule.scheduledJobs[jobName]; // ✅ Важно: полное удаление из памяти
            });

            // Генерируем три случайных времени с минимальным интервалом 30 минут
            const times = [];
            const minInterval = config.DUEL.MIN_INTERVAL_MINUTES;

            while (times.length < config.DUEL.QUOTES_PER_DAY) {
                const hour = Math.floor(Math.random() * (18 - 6 + 1)) + 6; // 6:00–18:00 CET
                const minute = Math.floor(Math.random() * 60);
                const timeValue = hour * 60 + minute;

                // Проверяем, что время не слишком близко к уже выбранным
                if (!times.some(t => Math.abs(t - timeValue) < minInterval)) {
                    times.push(timeValue);
                }
            }

            // Сортируем времена для последовательной отправки
            times.sort((a, b) => a - b);

            // Создаём новые задания для каждого времени
            times.forEach((timeValue, index) => {
                const hour = Math.floor(timeValue / 60);
                const minute = timeValue % 60;
                const jobName = `bash_quote_${index}_${Date.now()}`;

                const rule = new schedule.RecurrenceRule();
                rule.hour = hour;
                rule.minute = minute;
                rule.second = Math.floor(Math.random() * 60);
                rule.tz = config.DUEL.TIMEZONE;

                // Создаём одноразовое задание (выполняется только один раз в день)
                const job = schedule.scheduleJob(jobName, rule, async () => {
                    try {
                        const { message, comicUrl } = await getRandomBashQuote();
                        await this.bot.sendMessage(this.groupChatId, message, {
                            parse_mode: 'HTML',
                            disable_web_page_preview: true
                        });
                        if (comicUrl) {
                            await this.bot.sendPhoto(this.groupChatId, comicUrl);
                        }
                        console.log(`📝 Bash quote ${index + 1} sent at ${new Date().toLocaleString('en-US', { timeZone: config.DUEL.TIMEZONE })} CET`);

                        // Автоматически удаляем выполненное задание
                        if (schedule.scheduledJobs[jobName]) {
                            schedule.scheduledJobs[jobName].cancel();
                            delete schedule.scheduledJobs[jobName];
                            console.log(`🗑️ Auto-removed completed job: ${jobName}`);
                        }
                    } catch (error) {
                        console.error(`❌ Ошибка отправки случайной цитаты ${index + 1}:`, error.message);
                    }
                });

                console.log(`⏰ Scheduled bash quote ${index + 1} for ${hour}:${minute.toString().padStart(2, '0')} CET (Job: ${jobName})`);
            });

            // Выводим все текущие задачи для проверки
            const currentJobs = Object.keys(schedule.scheduledJobs);
            console.log(`📋 Total scheduled jobs after reset: ${currentJobs.length}`, currentJobs);
            console.log(`🎯 Created ${config.DUEL.QUOTES_PER_DAY} new bash quote jobs for today`);
        });

        // Автоматические проверки новостей "Дичь" (понедельники)
        config.SCHEDULES.WEIRD_NEWS_CHECK.forEach((cronTime, index) => {
            schedule.scheduleJob(`weird_news_check_${index}`, cronTime, async () => {
                try {
                    console.log(`🔍 Checking weird news (attempt ${index + 1}/4)`);
                    const newsResult = await newsChecker.checkWeirdNews();

                    if (newsResult) {
                        await this.bot.sendMessage(this.groupChatId, newsResult.message, {
                            parse_mode: 'HTML',
                            disable_web_page_preview: true
                        });
                        console.log(`📰 New weird news sent automatically!`);
                    }
                } catch (error) {
                    console.error(`❌ Error in weird news check ${index + 1}:`, error.message);
                }
            });
            console.log(`⏰ Scheduled weird news check ${index + 1} for ${cronTime}`);
        });

        // Автоматические проверки новостей "Муза" (пятницы)
        config.SCHEDULES.MUSE_NEWS_CHECK.forEach((cronTime, index) => {
            schedule.scheduleJob(`muse_news_check_${index}`, cronTime, async () => {
                try {
                    console.log(`🔍 Checking muse news (attempt ${index + 1}/4)`);
                    const newsResult = await newsChecker.checkMuseNews();

                    if (newsResult) {
                        await this.bot.sendMessage(this.groupChatId, newsResult.message, {
                            parse_mode: 'HTML',
                            disable_web_page_preview: true
                        });
                        console.log(`📰 New muse news sent automatically!`);
                    }
                } catch (error) {
                    console.error(`❌ Error in muse news check ${index + 1}:`, error.message);
                }
            });
            console.log(`⏰ Scheduled muse news check ${index + 1} for ${cronTime}`);
        });

        // Обработка бросков кубика
        this.bot.on('message', async (msg) => {
            if (msg.dice && msg.dice.emoji === '🎲' && duelManager.duelActive) {
                const userId = msg.from.id;
                const botId = (await this.bot.getMe()).id;
                if (userId === botId) return; // Игнорируем кубики от бота

                const username = msg.from.username ? msg.from.username.replace(/^@/, '') : msg.from.first_name;
                const diceValue = msg.dice.value;
                const added = await duelManager.addResult(userId, username, diceValue);
                if (added) {
                    setTimeout(() => {
                        this.bot.sendMessage(msg.chat.id, `${username}, результат броска: ${diceValue}. Результат зафиксирован.`);
                    }, config.TIMEOUTS.DICE_RESPONSE);
                }
            }
        });

        // Команда !hof
        this.bot.onText(/!hof/, async (msg) => {
            const hallOfFame = await duelManager.getHallOfFame();
            let message = '🏆 Зал славы:\n';
            if (hallOfFame.length === 0) {
                message += 'Пока нет победителей.';
            } else {
                hallOfFame.sort((a, b) => b.wins - a.wins); // Сортировка по победам
                hallOfFame.forEach((entry, index) => {
                    message += `${index + 1}. ${entry.username}: ${entry.wins} побед\n`;
                });
            }
            this.bot.sendMessage(msg.chat.id, message);
        });
    }
}

module.exports = Bot;