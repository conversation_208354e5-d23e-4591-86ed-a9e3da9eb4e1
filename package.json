{"name": "albus<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "Telegram бот для группового чата с функциями погоды, новостей, дуэлей и случайных цитат", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "test": "echo \"Error: no test specified\" && exit 1", "docker:build": "docker build -t albus-bot .", "docker:run": "docker run -d --name albus-bot --env-file .env -v $(pwd)/data:/app/data albus-bot"}, "keywords": ["telegram", "bot", "weather", "news", "games", "nodejs"], "author": "", "license": "ISC", "type": "commonjs", "engines": {"node": ">=18.0.0"}, "dependencies": {"axios": "^1.8.4", "cheerio": "^1.0.0", "dotenv": "^16.5.0", "iconv-lite": "^0.6.3", "node-schedule": "^2.1.1", "node-telegram-bot-api": "^0.66.0"}}