# Albus Bot JS

Telegram бот для группового чата с функциями погоды, новостей, дуэлей и случайных цитат.

## ✨ Функции

### 🤖 Основные команды

- `/start` - Начать работу с ботом
- `/help` - Показать список команд
- `/weather` - Показать текущую погоду
- `/admin` - Меню администратора (только для админа в ЛС)

### 🎲 Дуэли

- `!hof` - Зал славы победителей дуэлей
- Ежедневные дуэли с 12:00 до 15:00 MSK
- Бросок кубика 🎲 для участия

### 📰 Автоматические сообщения

- **08:00 MSK** - Утреннее сообщение (погода, курсы, новости, факт дня)
- **20:00 MSK** - Вечернее сообщение
- **3 раза в день** - Случайные цитаты с Башорга
- **Понедельники** - Автоматические проверки новостей "Дичь" (11:00, 14:00, 17:00, 21:00 MSK)
- **Пятницы** - Автоматические проверки новостей "Муза" (11:00, 14:00, 17:00, 21:00 MSK)

### 🌤️ Погода

- Волжский (основная локация)
- Валенсия, Испания
- Данные от Yandex Weather API

## 🚀 Быстрый запуск

### Требования

- Node.js 18+
- Docker (опционально)

### Настройка

1. **Клонируйте репозиторий**

```bash
git clone <repository-url>
cd AlbusBotJS
```

2. **Установите зависимости**

```bash
npm install
```

3. **Создайте файл .env**

```bash
# Скопируйте .env.example и заполните своими данными
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
GROUP_ID=-1001234567890
ADMIN_ID=123456789
API_WEATHER_KEY=your_yandex_weather_api_key_here
```

4. **Запустите бота**

```bash
npm start
```

### 🐳 Запуск с Docker

```bash
# Сборка образа
docker build -t albus-bot .

# Запуск контейнера
docker run -d --name albus-bot \
  --env-file .env \
  -v $(pwd)/data:/app/data \
  albus-bot
```

## 📝 Получение API ключей

### Telegram Bot Token

1. Напишите @BotFather в Telegram
2. Создайте нового бота командой `/newbot`
3. Скопируйте полученный токен

### Yandex Weather API

1. Зарегистрируйтесь на https://developer.tech.yandex.ru/
2. Создайте проект и получите API ключ для Weather API

### ID группы и админа

1. Добавьте @userinfobot в группу для получения GROUP_ID
2. Напишите боту в ЛС для получения ADMIN_ID

## 📂 Структура проекта

```
├── src/
│   ├── bot.js          # Основной класс бота
│   ├── config.js       # Конфигурация и константы
│   ├── dailyMessage.js # Ежедневные сообщения и новости
│   ├── duel.js         # Система дуэлей
│   ├── newsChecker.js  # Автоматические проверки новостей
│   ├── weather.js      # Модуль погоды
│   └── index.js        # Точка входа
├── data/               # Данные (создается автоматически)
├── Dockerfile
├── package.json
└── README.md
```
